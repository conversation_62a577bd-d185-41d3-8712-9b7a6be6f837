{"workflow": {"id": "generic_banking_workflow", "name": "Generic Banking Support Flow", "version": "1.0", "start": "state_greeting", "allowed_actions": ["Creating a new bank account", "Asking about exchange rates", "Checking account balance", "Requesting a loan", "Closing an account"], "prohibited_actions": ["Do not share account name and password", "Do not disclose sensitive information"], "states": {"state_greeting": {"id": "state_greeting", "type": "input", "layer2_id": "l2_try_agents", "expected_input": [], "expected_output": ["audio_path", "latencyTTS"], "transitions": [{"condition": "intent == 'check_balance'", "target": "state_check_balance"}, {"condition": "intent == 'loan_inquiry'", "target": "state_loan_inquiry"}], "allowed_tools": ["STT", "LLM", "TTS", "CACHE"]}, "state_check_balance": {"id": "state_check_balance", "type": "inform", "layer2_id": "l2_try_agents", "expected_input": ["account_id"], "expected_output": ["account_balance"], "transitions": [{"condition": "true", "target": "state_goodbye"}], "allowed_tools": ["LLM", "TTS"]}, "state_loan_inquiry": {"id": "state_loan_inquiry", "type": "inform", "layer2_id": "l2_try_agents", "expected_input": ["loan_amount"], "expected_output": ["loan_eligibility"], "transitions": [{"condition": "true", "target": "state_goodbye"}], "allowed_tools": ["LLM", "TTS"]}, "state_goodbye": {"id": "state_goodbye", "type": "end", "layer2_id": "l2_try_agents", "expected_input": [], "expected_output": ["exit_signal"], "transitions": [], "allowed_tools": ["TTS"]}}}}