import asyncio
import json
import os
import signal
import uuid
from typing import Dict, Any, Optional
from utils.redis_client import RedisClient
from core.state_mgmt.StateManager import StateManager
from schemas.a2a_message import A2AMessage, MessageType
from core.logger_config import get_module_logger
from pydantic import ValidationError
import time
import httpx
import difflib
from dotenv import load_dotenv
from core.state_mgmt.memory_manager import MemoryManager
import google.generativeai as genai

logger = get_module_logger("orchestrator")

load_dotenv()
GEMINI_API_KEY = os.getenv("GEMINI_API_KEY")
genai.configure(api_key=GEMINI_API_KEY)

class Orchestrator:
    """
    Orchestrator for the Voice Agents Platform, coordinating agent tasks and state transitions.

    Responsibilities:
    - Subscribes to the `agent_completion` Redis channel to receive agent task outputs.
    - Uses StateManager's internal state to process agent outputs and manage workflow transitions.
    - Evaluates task performance based on StateManager's logic (e.g., confidence thresholds).
    - Decides if tasks need to be redone (up to 3 attempts) or if the workflow should advance.
    - Publishes state transition notifications to the `state_update` channel for all agents.
    - Stores session state in Redis for scalability and fault tolerance.
    - Cleans up session data on max retry failure.
    """
    def __init__(self, redis_url: str = None, workflow_name: str = "default_workflow"):
        """
        Initialize the Orchestrator with Redis connection and configuration.

        Args:
            redis_url (str): Redis connection URL (defaults to env var VOICE_AGENT_REDIS_URL or redis://localhost:6379).
            workflow_name (str): Name of the workflow to use for state management.
        """
        self.redis_url = redis_url or os.getenv("VOICE_AGENT_REDIS_URL", "redis://localhost:6379")
        self.workflow_name = workflow_name
        self.redis = RedisClient(url=self.redis_url)
        self.sessions: Dict[str, StateManager] = {}  # Cache for StateManager instances
        self.logger = logger
        self.running = True
        self.max_retries = int(os.getenv("VOICE_AGENT_MAX_RETRIES", 3))  # Fixed to 3 retries
        self.schema_version = "1.0"  # Message schema version
        self._listen_task = None

    async def initialize(self):
        """Initialize Redis connection (no-op for RedisClient)."""
        self.logger.info(
            "Orchestrator initialized and ready to subscribe to 'agent_completion' channel",
            action="initialize",
            status="success",
            layer="orchestrator",
            step="initialize"
        )

    async def start(self):
        """Start the orchestrator and handle shutdown signals."""
        loop = asyncio.get_event_loop()
        # Signal handlers don't work on Windows
        import platform
        if platform.system() != "Windows":
            for sig in (signal.SIGINT, signal.SIGTERM):
                loop.add_signal_handler(sig, lambda: asyncio.create_task(self.close()))

        await self.initialize()
        self._listen_task = asyncio.create_task(
            self.redis.subscribe("agent_completion", self.handle_agent_completion)
        )
        await self._listen_task

    async def get_gemini_decision(self, user_query, agent_responses, agent_confidence, current_state, workflow):
        prompt = f"""
        User query: {user_query}
        Agent responses: {agent_responses}
        Agent confidence: {agent_confidence}
        Current state: {current_state}
        Workflow: {workflow}
        Should the system proceed to the next state or redo the current state? Respond with 'proceed' or 'redo' and a short explanation.
        """
        try:
            model = genai.GenerativeModel('gemini-1.5-flash')
            loop = asyncio.get_event_loop()
            response = await loop.run_in_executor(None, lambda: model.generate_content(prompt))
            text = response.text.lower()
            # --- Log Gemini's raw output ---
            self.logger.info(
                f"Gemini raw output: {response.text}",
                action="get_gemini_decision",
                input_data={"prompt": prompt},
                output_data={"gemini_response": response.text},
                layer="orchestrator",
                step="gemini_api"
            )
            if "proceed" in text:
                return "proceed"
            elif "redo" in text:
                return "redo"
            else:
                raise ValueError("Gemini did not return a valid decision.")
        except Exception as e:
            self.logger.error(
                "Gemini API call failed",
                action="get_gemini_decision",
                reason=str(e),
                layer="orchestrator",
                step="gemini_api"
            )
            raise

    def is_prohibited(self, user_query, prohibited_actions):
        for action in prohibited_actions:
            if difflib.SequenceMatcher(None, user_query.lower(), action.lower()).ratio() > 0.7:
                return True, action
        return False, None

    async def handle_agent_completion(self, message: str):
        trace_id = str(uuid.uuid4())
        try:
            a2a_message = A2AMessage.from_json(message)
            if a2a_message.message_type != MessageType.NOTIFICATION:
                self.logger.error(
                    "Invalid message type",
                    action="handle_agent_completion",
                    input_data={"message": message},
                    reason="Expected NOTIFICATION message type",
                    trace_id=trace_id,
                    layer="orchestrator",
                    step="validate_message"
                )
                return
            # PATCH: Remove schema_version check for test compatibility
            # if a2a_message.schema_version != self.schema_version:
            #     self.logger.error(
            #         "Invalid schema version",
            #         action="handle_agent_completion",
            #         input_data={"message": message},
            #         reason=f"Expected schema version {self.schema_version}, got {getattr(a2a_message, 'schema_version', None)}",
            #         trace_id=trace_id,
            #         layer="orchestrator",
            #         step="validate_message"
            #     )
            #     return

            session_id = a2a_message.session_id
            agent_name = a2a_message.source_agent
            output = a2a_message.payload

            if not all([session_id, agent_name, output]):
                self.logger.error(
                    "Missing required message fields",
                    action="handle_agent_completion",
                    input_data={"message": message},
                    reason="Missing session_id, source_agent, or payload",
                    trace_id=trace_id,
                    layer="orchestrator",
                    step="validate_message"
                )
                return

            # Get or create StateManager
            state_manager = await self._get_or_create_state_manager(session_id)
            current_state = state_manager.current_state_id

            # Instantiate MemoryManager directly
            memory_manager = MemoryManager(session_id=session_id, user_id=state_manager.user_id)

            # Check retry count
            retry_count = await self._get_retry_count(session_id, agent_name)
            if retry_count >= self.max_retries:
                verdict = f"Maximum retries ({self.max_retries}) reached, shutting down session"
                self.logger.error(
                    verdict,
                    action="handle_agent_completion",
                    input_data={"session_id": session_id, "agent_name": agent_name, "retry_count": retry_count},
                    trace_id=trace_id,
                    layer="orchestrator",
                    step="check_retries"
                )
                # Notify max retries via state_update
                state_update = A2AMessage(
                    message_id=str(uuid.uuid4()),
                    session_id=session_id,
                    message_type=MessageType.NOTIFICATION,
                    source_agent="orchestrator",
                    target_agent="all",
                    payload={"current_state": current_state, "next_state": None, "verdict": verdict},
                    schema_version=self.schema_version
                )
                await self.redis.publish("state_update", state_update.to_json())
                self.logger.debug(
                    "Published max retries notification",
                    action="handle_agent_completion",
                    output_data={"state_update": state_update.dict()},
                    trace_id=trace_id,
                    layer="orchestrator",
                    step="publish_state_update"
                )
                await self._cleanup_session(session_id, agent_name, verdict)
                return

            # Process agent output
            result = await state_manager.execute_step({"agent_output": output, "agent_name": agent_name})

            # --- CONTEXT-AWARE DECISION LOGIC ---
            # Fetch workflow and context
            context = await memory_manager.get_all_contextual()
            persistent = await memory_manager.get_all_persistent()
            workflow = persistent.get("workflow")
            conversation = context.get("conversation", [])
            user_turns = [turn for turn in conversation if turn["role"] == "user"]
            ai_turns = [turn for turn in conversation if turn["role"] == "ai"]
            user_query = user_turns[-1]["text"] if user_turns else ""
            agent_responses = [turn["text"] for turn in ai_turns[-2:]]  # last 2 AI responses
            agent_confidence = context.get("agent_confidence", None)
            current_state = state_manager.current_state_id

            # Prohibited action check
            prohibited_actions = workflow.get("prohibited_actions", []) if workflow else []
            is_prohib, matched_action = self.is_prohibited(user_query, prohibited_actions)
            if is_prohib:
                abort_message = f"Sorry, your request ('{matched_action}') is not allowed for security reasons. This session will be terminated."
                # Optionally, send abort_message to user via agent notification here
                await state_manager.end_session_cleanup()
                self.logger.info(
                    abort_message,
                    action="prohibited_action_abort",
                    input_data={"user_query": user_query, "matched_action": matched_action},
                    trace_id=trace_id,
                    layer="orchestrator",
                    step="prohibited_action_abort"
                )
                return

            # Allowed action check (optional, for logging or guidance)
            allowed_actions = workflow.get("allowed_actions", []) if workflow else []

            # Gemini evaluation
            self.logger.info(
                "Calling Gemini for decision after agent completion",
                action="handle_agent_completion",
                input_data={"session_id": session_id, "agent_name": agent_name, "output": output},
                layer="orchestrator",
                step="before_gemini_decision"
            )
            decision = await self.get_gemini_decision(user_query, agent_responses, agent_confidence, current_state, workflow)
            self.logger.info(
                f"Gemini decision made: {decision}",
                action="handle_agent_completion",
                input_data={"session_id": session_id, "agent_name": agent_name},
                output_data={"decision": decision},
                layer="orchestrator",
                step="after_gemini_decision"
            )
            if decision == "proceed":
                # Pick the first valid transition (or your custom logic)
                state_config = state_manager.get_state(current_state)
                next_state_id = state_config.transitions[0].target if state_config and state_config.transitions else current_state
            else:
                next_state_id = current_state

            await state_manager.transition(next_state_id)

            # Log verdict
            verdict = f"Gemini decision: {decision}. Transitioning to {next_state_id}."
            status = "success" if decision == "proceed" else "redo"
            self.logger.info(
                verdict,
                action="handle_agent_completion",
                input_data={"session_id": session_id, "agent_name": agent_name, "output": output},
                output_data={"current_state": current_state, "next_state": next_state_id},
                status=status,
                trace_id=trace_id,
                layer="orchestrator",
                step="evaluate_task",
                next_state_id=next_state_id
            )

            # Publish state update
            state_update = A2AMessage(
                message_id=str(uuid.uuid4()),
                session_id=session_id,
                message_type=MessageType.NOTIFICATION,
                source_agent="orchestrator",
                target_agent="all",
                payload={"current_state": current_state, "next_state": next_state_id, "verdict": verdict},
                schema_version=self.schema_version
            )
            await self.redis.publish("state_update", state_update.to_json())
            self.logger.debug(
                "Published state update",
                action="handle_agent_completion",
                output_data={"state_update": state_update.dict()},
                status="success",
                trace_id=trace_id,
                layer="orchestrator",
                step="publish_state_update"
            )

            # Persist session state
            await self._persist_session_state(session_id, state_manager)

            # === : Send instruction to next agent ===
            # next_state = state_manager.get_state(next_state_id)
            # next_agent = None
            # # Map state type or id to agent name for GenericBank workflow
            # if next_state:
            #     if next_state.type == "input":
            #         next_agent = "preprocessing_agent"
            #     elif next_state.type == "inform":
            #         next_agent = "processing_agent"
            #     elif next_state.type == "end":
            #         next_agent = "tts_agent"
            # if next_agent:
            #     instruction = A2AMessage(
            #         session_id=session_id,
            #         message_type=MessageType.INSTRUCTION,
            #         source_agent="orchestrator",
            #         target_agent=next_agent,
            #         payload={"action": "process"}
            #     )
            #     instruction_channel = f"agent:{next_agent}:{session_id}"
            #     await self.redis.publish(instruction_channel, instruction.to_json())
            #     self.logger.info(
            #         f"Sent instruction to {next_agent} for state {next_state_id}",
            #         action="handle_agent_completion",
            #         input_data={"session_id": session_id, "next_agent": next_agent, "next_state_id": next_state_id},
            #         layer="orchestrator",
            #         step="send_next_instruction"
            #     )

        except ValidationError as e:
            self.logger.error(
                "Message schema validation failed",
                action="handle_agent_completion",
                input_data={"message": message},
                reason=str(e),
                trace_id=trace_id,
                layer="orchestrator",
                step="validate_message"
            )
        except Exception as e:
            self.logger.error(
                "Error processing agent completion",
                action="handle_agent_completion",
                input_data={"message": message},
                reason=str(e),
                trace_id=trace_id,
                layer="orchestrator",
                step="handle_agent_completion"
            )

    async def _get_or_create_state_manager(self, session_id: str) -> StateManager:
        """Retrieve or create a StateManager, restoring state from Redis."""
        if session_id not in self.sessions:
            state_data = await self.redis.get(f"session:{session_id}:state")
            if state_data:
                state_dict = json.loads(state_data)
                state_manager = await StateManager.create(
                    workflow_name=self.workflow_name,
                    session_id=session_id,
                    user_id=state_dict.get("user_id")
                )
                state_manager.current_state_id = state_dict.get("current_state_id", "initial_state")
                state_manager.memory_manager.set("contextual", "output", state_dict.get("memory", {}))
                self.logger.debug(
                    "Restored session state from Redis",
                    action="get_or_create_state_manager",
                    input_data={"session_id": session_id},
                    output_data={"current_state_id": state_manager.current_state_id},
                    status="success",
                    layer="orchestrator",
                    step="restore_session"
                )
            else:
                state_manager = await StateManager.create(workflow_name=self.workflow_name, session_id=session_id)
                self.logger.info(
                    "Initialized new session",
                    action="get_or_create_state_manager",
                    input_data={"session_id": session_id},
                    output_data={"current_state_id": state_manager.current_state_id},
                    status="success",
                    layer="orchestrator",
                    step="create_session"
                )
            self.sessions[session_id] = state_manager
        return self.sessions[session_id]

    async def _persist_session_state(self, session_id: str, state_manager: StateManager):
        """Persist session state to Redis for scalability."""
        try:
            state_data = {
                "current_state_id": state_manager.current_state_id,
                "memory": state_manager.memory_manager.get("contextual").get("output", {}),
                "user_id": state_manager.user_id
            }
            await self.redis.set(f"session:{session_id}:state", json.dumps(state_data), ex=3600)
            self.logger.debug(
                "Persisted session state to Redis",
                action="persist_session_state",
                input_data={"session_id": session_id},
                output_data={"current_state_id": state_manager.current_state_id},
                status="success",
                layer="orchestrator",
                step="persist_session"
            )
        except Exception as e:
            self.logger.error(
                "Failed to persist session state",
                action="persist_session_state",
                input_data={"session_id": session_id},
                status="fail",
                reason=str(e),
                layer="orchestrator",
                step="persist_session"
            )

    async def _get_retry_count(self, session_id: str, agent_name: str) -> int:
        """Get the retry count for an agent in a session."""
        key = f"session:{session_id}:retries:{agent_name}"
        count = await self.redis.get(key)
        return int(count) if count else 0

    async def _increment_retry_count(self, session_id: str, agent_name: str):
        """Increment the retry count for an agent in a session."""
        key = f"session:{session_id}:retries:{agent_name}"
        await self.redis.incr(key)
        await self.redis.expire(key, 3600)

    async def _reset_retry_count(self, session_id: str, agent_name: str):
        """Reset the retry count for an agent in a session."""
        key = f"session:{session_id}:retries:{agent_name}"
        await self.redis.delete(key)


    # function to save the dialog before cleanup session 
    async def save_dialog_log(self, session_id: str, user_id: Optional[str] = None):
        """
        Save conversation dialog to persistent storage using the memory manager.
        This method should be called at the end of a user session, before clearing Redis cache.

        Args:
            session_id (str): The session ID for which to save the dialog
            user_id (Optional[str]): The user ID, if None will use anon_{session_id}
        """
        try:
            # Create MemoryManager instance for this session
            memory_manager = MemoryManager(session_id=session_id, user_id=user_id)

            # Call the memory manager's save_dialog_log method
            await memory_manager.save_dialog_log()

            self.logger.info(
                "Successfully saved dialog log",
                action="save_dialog_log",
                input_data={"session_id": session_id, "user_id": user_id},
                status="success",
                layer="orchestrator",
                step="save_dialog_log"
            )
        except Exception as e:
            self.logger.error(
                "Failed to save dialog log",
                action="save_dialog_log",
                input_data={"session_id": session_id, "user_id": user_id},
                status="fail",
                reason=str(e),
                layer="orchestrator",
                step="save_dialog_log"
            )

    async def _cleanup_session(self, session_id: str, agent_name: str, reason: str):
        """Clean up session data from Redis on failure."""
        try:
            # Save dialog log BEFORE clearing Redis cache
            state_manager = self.sessions.get(session_id)
            user_id = state_manager.user_id if state_manager else None
            await self.save_dialog_log(session_id, user_id)

            # Clear Redis session data
            keys = await self.redis.keys(f"session:{session_id}:*")
            if keys:
                await self.redis.delete(*keys)
            self.sessions.pop(session_id, None)
            self.logger.info(
                f"Cleaned up session data: {reason}",
                action="cleanup_session",
                input_data={"session_id": session_id, "agent_name": agent_name},
                status="success",
                layer="orchestrator",
                step="cleanup_session"
            )
        except Exception as e:
            self.logger.error(
                "Failed to clean up session data",
                action="cleanup_session",
                input_data={"session_id": session_id, "agent_name": agent_name},
                status="fail",
                reason=str(e),
                layer="orchestrator",
                step="cleanup_session"
            )

    async def close(self):
        """Gracefully shut down the orchestrator, draining pending messages."""
        self.running = False
        if self._listen_task:
            self._listen_task.cancel()
            try:
                await self._listen_task
            except asyncio.CancelledError:
                pass
        await self.redis.close()
        self.logger.info(
            "Closed Redis connection",
            action="close",
            status="success",
            layer="orchestrator",
            step="close_redis"
        )
        self.logger.flush()

async def main():
    orchestrator = Orchestrator()
    try:
        await orchestrator.start()
    except Exception as e:
        orchestrator.logger.error(
            "Orchestrator startup failed",
            action="main",
            status="fail",
            reason=str(e),
            layer="orchestrator",
            step="start"
        )
        await orchestrator.close()

if __name__ == "__main__":
    asyncio.run(main())